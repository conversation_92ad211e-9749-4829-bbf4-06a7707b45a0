<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trending Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Public Sans', sans-serif;
            background-color: #ffffff;
        }
        
        .trending-podcasts {
            position: relative;
            width: 4.28rem;
            height: 9.26rem;
            background-color: #ffffff;
            margin: 0 auto;
        }
        
        .group-168 {
            position: absolute;
            left: 0.32rem;
            top: 1.28rem;
            width: 3.64rem;
            height: 0.64rem;
        }
        
        .rectangle-1569 {
            position: absolute;
            width: 3.64rem;
            height: 0.64rem;
            background-color: rgba(31, 31, 31, 0.08);
            border-radius: 0.32rem;
        }
        
        .group-172 {
            position: absolute;
            left: 0.2rem;
            top: 0.2rem;
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/group-172.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .search-text {
            position: absolute;
            left: 0.56rem;
            top: 0.22rem;
            font-weight: 500;
            font-size: 0.16rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.5);
        }
        
        .trending-title {
            position: absolute;
            left: 0.72rem;
            top: 0.58rem;
            font-weight: 700;
            font-size: 0.24rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .arrow-circle-left-fill {
            position: absolute;
            left: 0.32rem;
            top: 0.48rem;
            width: 0.48rem;
            height: 0.48rem;
            background-image: url('./images/arrow-circle-left-fill.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .group-155 {
            position: absolute;
            left: 0.48rem;
            top: 0.65rem;
            width: 0.16rem;
            height: 0.14rem;
            background-image: url('./images/group-155.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .mask-group-1 {
            position: absolute;
            left: 0.32rem;
            top: 2.24rem;
            width: 1.74rem;
            height: 1.5rem;
            background-image: url('./images/image-5.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .mask-group-2 {
            position: absolute;
            left: 2.22rem;
            top: 2.24rem;
            width: 1.74rem;
            height: 1.5rem;
            background-image: url('./images/image-7.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .mask-group-3 {
            position: absolute;
            left: 0.32rem;
            top: 4.54rem;
            width: 1.74rem;
            height: 1.5rem;
            background-image: url('./images/image-10.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .mask-group-4 {
            position: absolute;
            left: 2.22rem;
            top: 4.54rem;
            width: 1.74rem;
            height: 1.5rem;
            background-image: url('./images/image-6.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .podcast-title-1 {
            position: absolute;
            left: 0.32rem;
            top: 3.9rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .podcast-title-2 {
            position: absolute;
            left: 2.22rem;
            top: 3.9rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .podcast-title-3 {
            position: absolute;
            left: 0.32rem;
            top: 6.2rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .podcast-title-4 {
            position: absolute;
            left: 2.22rem;
            top: 6.2rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .podcast-category-1 {
            position: absolute;
            left: 0.32rem;
            top: 4.14rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .podcast-category-2 {
            position: absolute;
            left: 2.22rem;
            top: 4.14rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .podcast-category-3 {
            position: absolute;
            left: 0.32rem;
            top: 6.44rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .podcast-category-4 {
            position: absolute;
            left: 2.22rem;
            top: 6.44rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .recommended-title {
            position: absolute;
            left: 0.32rem;
            top: 6.92rem;
            font-weight: 700;
            font-size: 0.24rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .group-158 {
            position: relative;
            position: absolute;
            left: 0.32rem;
            top: 7.44rem;
            width: 3.64rem;
            height: 0.96rem;
        }
        
        .mask-group-5 {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 1.08rem;
            height: 0.96rem;
            background-image: url('./images/image-9.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .rectangle-1573 {
            position: absolute;
            right: 0rem;
            top: 0.24rem;
            width: 0.48rem;
            height: 0.48rem;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 0.24rem;
        }
        
        .polygon-1 {
            position: absolute;
            right: 0.15rem;
            top: 0.39rem;
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/polygon-1.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .mind-map-title {
            position: absolute;
            left: 1.24rem;
            top: 0.14rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .mind-map-category {
            position: absolute;
            left: 1.24rem;
            top: 0.41rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .mind-map-duration {
            position: absolute;
            left: 1.24rem;
            top: 0.65rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .group-159 {
            position: relative;
            position: absolute;
            left: 0.32rem;
            top: 8.64rem;
            width: 3.64rem;
            height: 0.96rem;
        }
        
        .mask-group-6 {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 1.08rem;
            height: 0.96rem;
            background-image: url('./images/image-2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 0.16rem;
        }
        
        .rectangle-1575 {
            position: absolute;
            right: 0rem;
            top: 0.24rem;
            width: 0.48rem;
            height: 0.48rem;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 0.24rem;
        }
        
        .polygon-2 {
            position: absolute;
            right: 0.15rem;
            top: 0.39rem;
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/polygon-2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .true-crime-title {
            position: absolute;
            left: 1.24rem;
            top: 0.14rem;
            font-weight: 700;
            font-size: 0.16rem;
            line-height: 1.175;
            color: #1f1f1f;
        }
        
        .true-crime-category {
            position: absolute;
            left: 1.24rem;
            top: 0.41rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
        
        .true-crime-duration {
            position: absolute;
            left: 1.24rem;
            top: 0.65rem;
            font-weight: 400;
            font-size: 0.14rem;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }
    </style>
</head>
<body>
    <div class="trending-podcasts">
        <div class="group-168">
            <div class="rectangle-1569"></div>
            <div class="group-172"></div>
            <div class="search-text">Search the podcast here...</div>
        </div>
        
        <div class="trending-title">Trending Podcasts</div>
        <div class="arrow-circle-left-fill"></div>
        <div class="group-155"></div>
        
        <div class="mask-group-1"></div>
        <div class="mask-group-2"></div>
        <div class="mask-group-3"></div>
        <div class="mask-group-4"></div>
        
        <div class="podcast-title-1">Mind of an Entrepre...</div>
        <div class="podcast-title-2">Unravelling the Mind</div>
        <div class="podcast-title-3">A Tale of Writer</div>
        <div class="podcast-title-4">Addiction to Social!</div>
        
        <div class="podcast-category-1">Business</div>
        <div class="podcast-category-2">Healthy Lifestyle</div>
        <div class="podcast-category-3">Educational</div>
        <div class="podcast-category-4">Sociology</div>
        
        <div class="recommended-title">Recommended For You</div>
        
        <div class="group-158">
            <div class="mask-group-5"></div>
            <div class="rectangle-1573"></div>
            <div class="polygon-1"></div>
            <div class="mind-map-title">Mind map</div>
            <div class="mind-map-category">Health & Lifestyle</div>
            <div class="mind-map-duration">10 min</div>
        </div>
        
        <div class="group-159">
            <div class="mask-group-6"></div>
            <div class="rectangle-1575"></div>
            <div class="polygon-2"></div>
            <div class="true-crime-title">True Crime</div>
            <div class="true-crime-category">Investigation Theories</div>
            <div class="true-crime-duration">15 min</div>
        </div>
    </div>

    <script>
        (function () {
            const designWidth = 428; // 设计稿宽度
            const baseRem = 100;      // 设定 1rem = 100px，方便换算

            function setRootFontSize() {
                const html = document.documentElement;
                const clientWidth = html.clientWidth;

                // 让页面宽度和设计稿成等比缩放
                html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
            }

            setRootFontSize();
            window.addEventListener('resize', setRootFontSize);
        })();
    </script>
</body>
</html>
